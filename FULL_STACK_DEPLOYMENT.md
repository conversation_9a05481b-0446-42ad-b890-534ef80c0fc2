# SMSMali Full-Stack Deployment Guide
Complete guide for deploying both React frontend and Django backend on PythonAnywhere.

## 🎯 **Overview**
This guide will help you deploy the complete SMSMali application with:
- ⚛️ **React Frontend** (built with <PERSON>ite, Tai<PERSON><PERSON> CSS, DaisyUI, Three.js)
- 🐍 **Django Backend** (REST API with JWT authentication)
- 🌐 **PythonAnywhere Hosting** (production deployment)

## � **PythonAnywhere Free Plan Considerations**
- ❌ **No Node.js/npm**: Frontend must be built locally and committed to repo
- ✅ **Python Support**: Full Django backend support
- ✅ **Static Files**: Can serve pre-built React assets
- ⚠️ **Workaround**: Build frontend locally, commit dist files, deploy on PythonAnywhere

## �🚀 **Quick Deployment (Free Plan Compatible)**

### **Step 1: Build Frontend Locally (Required for Free Plan)**
```bash
# On your local development machine
cd c:\Users\<USER>\Documents\augment-projects\smsmali

# Build the React frontend
python build_frontend_local.py

# Commit and push the built files
git add -f frontend/dist/
git commit -m "build: Add React frontend production build for PythonAnywhere"
git push origin master
```

### **Step 2: Clone and Setup on PythonAnywhere**
```bash
# SSH into PythonAnywhere or use console
cd /home/<USER>

# Clone repository (use SSH if configured)
<NAME_EMAIL>:CooperSystems-commits/SMSMali.git
cd SMSMali

# Or update existing repository
git pull origin master
```

### **Step 3: Deploy Backend and Frontend**
```bash
# Run the complete deployment script (free plan compatible)
python3.10 deploy_simple.py --production

# This will:
# ✅ Create virtual environment
# ✅ Install Python dependencies
# ✅ Deploy pre-built React frontend
# ✅ Collect static files
# ✅ Run database migrations
# ✅ Create production settings
# ✅ Generate WSGI configuration
```

### **Step 3: Configure PythonAnywhere Web App**
```bash
# Generate web app configuration
python3.10 setup_pythonanywhere_webapp.py --validate --generate-config

# Follow the generated instructions to configure:
# - Source code directory
# - WSGI file
# - Virtual environment
# - Static file mappings
# - Environment variables
```

### **Step 4: Reload and Test**
1. **Reload** your PythonAnywhere web app
2. **Visit**: https://smsmali.pythonanywhere.com
3. **Test**: Frontend loads with React components
4. **Verify**: API endpoints work at `/api/`

## 🔧 **Manual Deployment (Step by Step)**

### **Backend Deployment**

#### **1. Python Environment Setup**
```bash
cd /home/<USER>/SMSMali

# Create virtual environment
python3.10 -m venv venv
source venv/bin/activate

# Upgrade pip
pip install --upgrade pip

# Install dependencies
pip install -r requirements.txt
```

#### **2. Django Configuration**
```bash
# Run migrations
python manage.py migrate --settings=smsmali.settings_production

# Create superuser (optional)
python manage.py createsuperuser --settings=smsmali.settings_production

# Test Django
python manage.py check --settings=smsmali.settings_production
```

### **Frontend Deployment**

#### **1. Build React App (if needed)**
```bash
cd frontend

# Install dependencies (if node_modules missing)
npm install

# Build for production
npm run build

# Return to project root
cd ..
```

#### **2. Deploy Frontend to Django**
```bash
# Run frontend deployment script
python3.10 deploy_frontend.py

# This will:
# ✅ Copy built React app to Django templates
# ✅ Copy assets to static files
# ✅ Configure Django to serve React app
```

#### **3. Collect Static Files**
```bash
# Collect all static files
python manage.py collectstatic --noinput --clear --settings=smsmali.settings_production
```

## 🌐 **PythonAnywhere Web App Configuration**

### **Web App Settings**
- **Domain**: `smsmali.pythonanywhere.com`
- **Python Version**: `3.10`
- **Source Code**: `/home/<USER>/SMSMali`
- **WSGI File**: `/var/www/smsmali_pythonanywhere_com_wsgi.py`
- **Virtual Environment**: `/home/<USER>/SMSMali/venv`

### **WSGI Configuration**
Edit `/var/www/smsmali_pythonanywhere_com_wsgi.py`:
```python
import os
import sys

# Set environment variables
os.environ['DJANGO_ENV'] = 'production'
os.environ['DJANGO_SECRET_KEY'] = 'your-generated-secret-key'
os.environ['DJANGO_SETTINGS_MODULE'] = 'smsmali.settings_production'

# Add project to Python path
path = '/home/<USER>/SMSMali'
if path not in sys.path:
    sys.path.insert(0, path)

# Import Django WSGI application
from django.core.wsgi import get_wsgi_application
application = get_wsgi_application()
```

### **Static Files Mapping**
| URL | Directory | Description |
|-----|-----------|-------------|
| `/static/` | `/home/<USER>/SMSMali/staticfiles` | All static files (CSS, JS, images) |
| `/media/` | `/home/<USER>/SMSMali/media` | User uploaded files |

### **Environment Variables**
Set in PythonAnywhere web interface or WSGI file:
```
DJANGO_ENV = production
DJANGO_SECRET_KEY = [generated-secret-key]
DJANGO_SETTINGS_MODULE = smsmali.settings_production
```

## 🎨 **Frontend Architecture**

### **React App Structure**
```
frontend/
├── src/
│   ├── components/     # Reusable UI components
│   ├── pages/         # Page components
│   ├── App.jsx        # Main app component
│   └── main.jsx       # Entry point
├── dist/              # Built production files
└── package.json       # Dependencies and scripts
```

### **Key Technologies**
- **React 18** with hooks and modern patterns
- **Vite** for fast development and building
- **Tailwind CSS** for utility-first styling
- **DaisyUI** for pre-built components
- **Three.js** with React Three Fiber for 3D graphics
- **Framer Motion** for animations
- **React Router** for client-side routing

### **Django Integration**
- React app built to `frontend/dist/`
- `index.html` served by Django templates
- Assets served through Django static files
- API calls to Django REST endpoints

## 🔍 **Testing the Deployment**

### **Frontend Tests**
```bash
# Test React app loads
curl -I https://smsmali.pythonanywhere.com/

# Check static assets
curl -I https://smsmali.pythonanywhere.com/static/assets/index-[hash].js
```

### **Backend Tests**
```bash
# Test API endpoints
curl https://smsmali.pythonanywhere.com/api/products/
curl https://smsmali.pythonanywhere.com/api/loans/

# Test admin panel
# Visit: https://smsmali.pythonanywhere.com/admin/
```

### **Full-Stack Tests**
1. **Homepage**: React components load and render
2. **Navigation**: Client-side routing works
3. **API Integration**: Frontend can fetch data from backend
4. **3D Graphics**: Three.js components render properly
5. **Responsive Design**: Works on mobile and desktop

## 🔧 **Troubleshooting**

### **Common Issues**

#### **Frontend Not Loading**
```bash
# Check if React files are in place
ls -la /home/<USER>/SMSMali/templates/index.html
ls -la /home/<USER>/SMSMali/staticfiles/assets/

# Check if frontend was built and committed
ls -la /home/<USER>/SMSMali/frontend/dist/

# Free Plan Issue: Missing frontend build
# Solution: Build locally and commit
# On your local machine:
python build_frontend_local.py
git add -f frontend/dist/
git commit -m "build: Add React frontend build"
git push origin master

# Then on PythonAnywhere:
git pull origin master
python3.10 deploy_simple.py --production
```

#### **API Endpoints Not Working**
```bash
# Check Django configuration
python manage.py check --settings=smsmali.settings_production

# Test URLs
python manage.py shell --settings=smsmali.settings_production
>>> from django.urls import reverse
>>> reverse('admin:index')
```

#### **Static Files Missing**
```bash
# Recollect static files
python manage.py collectstatic --noinput --clear --settings=smsmali.settings_production

# Check static files directory
ls -la /home/<USER>/SMSMali/staticfiles/
```

### **Debug Commands**
```bash
# Check deployment status
python3.10 setup_pythonanywhere_webapp.py --validate

# View Django logs
tail -f /home/<USER>/SMSMali/django.log

# Test frontend deployment
python3.10 deploy_frontend.py --copy-only
```

## 📋 **Deployment Checklist**

### **Before Deployment**
- [ ] Code committed and pushed to GitHub
- [ ] Frontend built (`npm run build`)
- [ ] Dependencies updated in requirements.txt
- [ ] Production settings configured

### **During Deployment**
- [ ] Repository cloned/updated on PythonAnywhere
- [ ] Virtual environment created and activated
- [ ] Python dependencies installed
- [ ] Frontend deployed and integrated
- [ ] Static files collected
- [ ] Database migrations applied
- [ ] WSGI file configured

### **After Deployment**
- [ ] Web app configured in PythonAnywhere
- [ ] Static file mappings added
- [ ] Environment variables set
- [ ] Web app reloaded
- [ ] Frontend loads correctly
- [ ] API endpoints working
- [ ] Admin panel accessible

## 🎉 **Success!**

Your SMSMali full-stack application should now be live at:
- **Main Site**: https://smsmali.pythonanywhere.com
- **Admin Panel**: https://smsmali.pythonanywhere.com/admin/
- **API Docs**: https://smsmali.pythonanywhere.com/api/docs/

## 🔄 **Updates and Maintenance**

### **Updating Code**
```bash
cd /home/<USER>/SMSMali
git pull origin master
python3.10 deploy_simple.py --production
# Reload web app in PythonAnywhere
```

### **Frontend Updates**
```bash
# If frontend code changed
python3.10 deploy_frontend.py
python manage.py collectstatic --noinput --clear
# Reload web app
```

### **Backend Updates**
```bash
# If Django code changed
source venv/bin/activate
pip install -r requirements.txt
python manage.py migrate --settings=smsmali.settings_production
python manage.py collectstatic --noinput --clear
# Reload web app
```

---

🎯 **Your SMSMali pawnshop application is now fully deployed with both React frontend and Django backend working together!**
