#!/usr/bin/env python3
"""
Local Frontend Build Script for SMSMali
=======================================

This script builds the React frontend locally and prepares it for
deployment on PythonAnywhere free plan (which doesn't support Node.js builds).

Usage:
    python build_frontend_local.py [--commit]

Author: SMSMali Development Team
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path
import argparse

class LocalFrontendBuilder:
    def __init__(self):
        self.project_root = Path.cwd()
        self.frontend_dir = self.project_root / "frontend"
        self.dist_dir = self.frontend_dir / "dist"
        
        print("🏗️ SMSMali Local Frontend Builder")
        print(f"📁 Project Root: {self.project_root}")
        print(f"⚛️ Frontend Dir: {self.frontend_dir}")
        print("-" * 50)

    def check_node_available(self):
        """Check if Node.js and npm are available"""
        try:
            subprocess.run(["node", "--version"], check=True, capture_output=True)
            subprocess.run(["npm", "--version"], check=True, capture_output=True)
            print("✅ Node.js and npm are available")
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("❌ Node.js or npm not found")
            print("   Please install Node.js from https://nodejs.org/")
            return False

    def check_frontend_exists(self):
        """Check if frontend directory exists"""
        if not self.frontend_dir.exists():
            print("❌ Frontend directory not found!")
            return False
        
        package_json = self.frontend_dir / "package.json"
        if not package_json.exists():
            print("❌ package.json not found in frontend directory!")
            return False
        
        print("✅ Frontend directory found")
        return True

    def install_dependencies(self):
        """Install npm dependencies"""
        print("\n📦 Installing npm dependencies...")
        
        try:
            os.chdir(self.frontend_dir)
            subprocess.run(["npm", "install"], check=True)
            os.chdir(self.project_root)
            print("✅ Dependencies installed")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install dependencies: {e}")
            return False
        finally:
            os.chdir(self.project_root)

    def build_frontend(self):
        """Build the React frontend"""
        print("\n🔨 Building React frontend...")
        
        try:
            os.chdir(self.frontend_dir)
            subprocess.run(["npm", "run", "build"], check=True)
            os.chdir(self.project_root)
            print("✅ Frontend build completed")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ Frontend build failed: {e}")
            return False
        finally:
            os.chdir(self.project_root)

    def verify_build(self):
        """Verify the build was successful"""
        print("\n🔍 Verifying build...")
        
        if not self.dist_dir.exists():
            print("❌ Dist directory not found")
            return False
        
        index_html = self.dist_dir / "index.html"
        if not index_html.exists():
            print("❌ index.html not found in dist")
            return False
        
        assets_dir = self.dist_dir / "assets"
        if not assets_dir.exists():
            print("❌ Assets directory not found")
            return False
        
        # Count assets
        asset_files = list(assets_dir.glob("*"))
        print(f"✅ Build verified: {len(asset_files)} asset files found")
        return True

    def prepare_for_git(self):
        """Prepare built files for git commit"""
        print("\n📋 Preparing for git commit...")
        
        # Check if dist is in .gitignore
        gitignore = self.project_root / ".gitignore"
        if gitignore.exists():
            with open(gitignore, 'r') as f:
                gitignore_content = f.read()
            
            if 'frontend/dist' in gitignore_content or 'dist/' in gitignore_content:
                print("⚠️ frontend/dist is in .gitignore")
                print("   You may need to force add it: git add -f frontend/dist/")
        
        print("✅ Ready for git commit")
        return True

    def show_commit_instructions(self):
        """Show instructions for committing the build"""
        print("\n📝 Git Commit Instructions:")
        print("=" * 30)
        print("# Add the built frontend files")
        print("git add frontend/dist/")
        print("")
        print("# Or force add if in .gitignore")
        print("git add -f frontend/dist/")
        print("")
        print("# Commit the build")
        print('git commit -m "build: Add React frontend production build for PythonAnywhere"')
        print("")
        print("# Push to repository")
        print("git push origin master")
        print("")
        print("🎯 After pushing, run deployment on PythonAnywhere:")
        print("   python3.10 deploy_simple.py --production")

    def build(self, commit=False):
        """Main build process"""
        if not self.check_node_available():
            return False
        
        if not self.check_frontend_exists():
            return False
        
        # Install dependencies if needed
        node_modules = self.frontend_dir / "node_modules"
        if not node_modules.exists():
            if not self.install_dependencies():
                return False
        
        # Build the frontend
        if not self.build_frontend():
            return False
        
        # Verify the build
        if not self.verify_build():
            return False
        
        # Prepare for git
        self.prepare_for_git()
        
        if commit:
            self.commit_build()
        else:
            self.show_commit_instructions()
        
        return True

    def commit_build(self):
        """Commit the build to git"""
        print("\n📤 Committing build to git...")
        
        try:
            # Add dist files
            subprocess.run(["git", "add", "-f", "frontend/dist/"], check=True)
            
            # Commit
            subprocess.run([
                "git", "commit", "-m", 
                "build: Add React frontend production build for PythonAnywhere\n\n" +
                "- Built with npm run build\n" +
                "- Ready for PythonAnywhere free plan deployment\n" +
                "- Includes all assets and index.html"
            ], check=True)
            
            print("✅ Build committed to git")
            print("\n📤 Push to GitHub:")
            print("   git push origin master")
            
        except subprocess.CalledProcessError as e:
            print(f"❌ Git commit failed: {e}")
            print("   You may need to commit manually")

def main():
    parser = argparse.ArgumentParser(description='Build SMSMali React frontend locally')
    parser.add_argument('--commit', action='store_true',
                       help='Automatically commit the build to git')
    
    args = parser.parse_args()
    
    builder = LocalFrontendBuilder()
    success = builder.build(commit=args.commit)
    
    if success:
        print("\n✅ Frontend build completed successfully!")
        print("\n🎯 Next steps:")
        print("1. Commit and push the built files to GitHub")
        print("2. Deploy on PythonAnywhere: python3.10 deploy_simple.py --production")
        print("3. The deployment will use the pre-built files (free plan compatible)")
        return 0
    else:
        print("\n❌ Frontend build failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
