{"webapp_info": {"username": "smsmali", "domain": "smsmali.pythonanywhere.com", "project_directory": "/home/<USER>/SMSMali", "python_version": "3.10"}, "paths": {"source_code": "/home/<USER>/SMSMali", "wsgi_file": "/home/<USER>/SMSMali/wsgi.py", "virtualenv": "/home/<USER>/SMSMali/venv", "static_root": "/home/<USER>/SMSMali/staticfiles", "media_root": "/home/<USER>/SMSMali/media"}, "static_files_mapping": [{"url": "/static/", "directory": "/home/<USER>/SMSMali/staticfiles", "description": "Django static files"}, {"url": "/media/", "directory": "/home/<USER>/SMSMali/media", "description": "User uploaded media files"}], "environment_variables": {"DJANGO_ENV": {"value": "production", "description": "Set Django to production mode"}, "DJANGO_SECRET_KEY": {"value": "%2m73vbn3uqcz94j=@!j25vj5^rs^z3db+=%jt*s$4)hq@4&96", "description": "Django secret key for security"}, "DJANGO_SETTINGS_MODULE": {"value": "smsmali.settings_production", "description": "Use production settings"}}, "security_checklist": ["Set DEBUG = False in production settings", "Configure ALLOWED_HOSTS properly", "Set secure SECRET_KEY", "Enable HTTPS redirects", "Configure CORS settings", "Set up proper logging"], "deployment_commands": ["cd /home/<USER>/SMSMali", "source venv/bin/activate", "python manage.py collectstatic --noinput --settings=smsmali.settings_production", "python manage.py migrate --settings=smsmali.settings_production"]}