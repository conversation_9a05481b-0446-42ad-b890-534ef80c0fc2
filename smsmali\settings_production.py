# Production settings for Python<PERSON>nywhere
import os
from .settings import *  # Import all base settings
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Security settings
DEBUG = False
ALLOWED_HOSTS = [
    'smsmali.pythonanywhere.com', 
    'www.smsmali.pythonanywhere.com',
    'localhost',
    '127.0.0.1'
]

# Secret key from environment variable or fallback
SECRET_KEY = os.environ.get('DJANGO_SECRET_KEY', 'your-fallback-secret-key-here')

# Database configuration
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
    }
}

# Static files configuration
STATIC_URL = '/static/'
STATIC_ROOT = BASE_DIR / 'staticfiles'
STATICFILES_DIRS = [
    BASE_DIR / 'static',
]

# Media files configuration
MEDIA_URL = '/media/'
MEDIA_ROOT = BASE_DIR / 'media'

# CORS settings for production
CORS_ALLOWED_ORIGINS = [
    "https://smsmali.pythonanywhere.com",
    "https://www.smsmali.pythonanywhere.com",
]

# Security settings
SECURE_SSL_REDIRECT = False  # PythonAnywhere handles SSL
SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True

# Additional settings
USE_TZ = True
TIME_ZONE = 'UTC'

# Logging
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': BASE_DIR / 'django.log',
        },
    },
    'loggers': {
        'django': {
            'handlers': ['file'],
            'level': 'INFO',
            'propagate': True,
        },
    },
}