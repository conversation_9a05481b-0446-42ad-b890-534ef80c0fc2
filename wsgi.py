# WSGI configuration for <PERSON><PERSON>ali on PythonAnywhere

import os
import sys

# Add your project directory to the sys.path
path = '/home/<USER>/SMSMali'
if path not in sys.path:
    sys.path.insert(0, path)

# Set the Django settings module
if os.environ.get('DJANGO_ENV') == 'production':
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'smsmali.settings_production')
else:
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'smsmali.settings')

# Import Django WSGI application
from django.core.wsgi import get_wsgi_application
application = get_wsgi_application()
